import React, { useState, useEffect } from 'react';
import { useParams, Link } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';
import EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';

interface Scholarship {
  id: number;
  title: string;
  thumbnail?: string;
  deadline: string;
  isOpen: boolean;
  country: string;
  level: string;
  description?: string;
  financialBenefitsSummary?: string;
  eligibilitySummary?: string;
}

interface LevelStatistics {
  level: string;
  totalScholarships: number;
  openScholarships: number;
  closedScholarships: number;
}

const ScholarshipsByLevel: React.FC = () => {
  const { level } = useParams<{ level: string }>();
  const { translations } = useLanguage();
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [allLevels, setAllLevels] = useState<string[]>([]);
  const [latestScholarships, setLatestScholarships] = useState<Scholarship[]>([]);
  const [statistics, setStatistics] = useState<LevelStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const decodedLevel = level ? decodeURIComponent(level) : '';

  useEffect(() => {
    if (decodedLevel) {
      fetchScholarshipsByLevel();
      fetchAllLevels();
      fetchLatestScholarships();
      fetchLevelStatistics();
    }
  }, [decodedLevel, currentPage]);

  const fetchScholarshipsByLevel = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/scholarships?level=${encodeURIComponent(decodedLevel)}&page=${currentPage}&limit=12`);
      if (response.ok) {
        const data = await response.json();
        setScholarships(data.data || []);
        setTotalPages(data.pagination?.totalPages || 1);
      }
    } catch (error) {
      console.error('Error fetching scholarships by level:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAllLevels = async () => {
    try {
      const response = await fetch('/api/scholarships/levels');
      if (response.ok) {
        const data = await response.json();
        setAllLevels(data.data?.map((item: any) => item.name) || []);
      }
    } catch (error) {
      console.error('Error fetching levels:', error);
    }
  };

  const fetchLatestScholarships = async () => {
    try {
      const response = await fetch('/api/scholarships?limit=6&orderBy=created_at&orderDirection=DESC');
      if (response.ok) {
        const data = await response.json();
        setLatestScholarships(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching latest scholarships:', error);
    }
  };

  const fetchLevelStatistics = async () => {
    try {
      const response = await fetch(`/api/scholarships/levels/statistics?level=${encodeURIComponent(decodedLevel)}`);
      if (response.ok) {
        const data = await response.json();
        setStatistics(data.data);
      }
    } catch (error) {
      console.error('Error fetching level statistics:', error);
    }
  };

  const handleScholarshipClick = (id: number) => {
    window.location.href = `/scholarships/${id}`;
  };

  const getLevelIcon = (levelName: string): string => {
    const icons: { [key: string]: string } = {
      'licence': '🎓',
      'bachelor': '🎓',
      'undergraduate': '🎓',
      'master': '🎯',
      'masters': '🎯',
      'graduate': '🎯',
      'doctorat': '🔬',
      'doctorate': '🔬',
      'phd': '🔬',
      'postdoc': '🧑‍🔬'
    };
    return icons[levelName.toLowerCase()] || '📚';
  };

  const getLevelColor = (levelName: string): string => {
    const colors: { [key: string]: string } = {
      'licence': 'blue',
      'bachelor': 'blue',
      'undergraduate': 'blue',
      'master': 'purple',
      'masters': 'purple',
      'graduate': 'purple',
      'doctorat': 'indigo',
      'doctorate': 'indigo',
      'phd': 'indigo',
      'postdoc': 'green'
    };
    return colors[levelName.toLowerCase()] || 'gray';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className={`bg-gradient-to-r from-${getLevelColor(decodedLevel)}-600 to-${getLevelColor(decodedLevel)}-700 text-white py-16`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center mb-6">
            <Link
              to="/scholarships"
              className={`flex items-center text-${getLevelColor(decodedLevel)}-200 hover:text-white transition-colors duration-200`}
            >
              <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Retour aux bourses
            </Link>
          </div>
          
          <div className="flex items-center mb-4">
            <div className="text-6xl mr-4">
              {getLevelIcon(decodedLevel)}
            </div>
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-2">
                Bourses de {decodedLevel}
              </h1>
              <p className={`text-xl text-${getLevelColor(decodedLevel)}-100`}>
                Découvrez toutes les opportunités pour ce niveau d'études
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Section */}
      {statistics && (
        <div className="bg-white py-12 border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className={`text-3xl font-bold text-${getLevelColor(decodedLevel)}-600 mb-2`}>
                  {statistics.totalScholarships}
                </div>
                <div className="text-gray-600">Total des bourses</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {statistics.openScholarships}
                </div>
                <div className="text-gray-600">Bourses ouvertes</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600 mb-2">
                  {statistics.closedScholarships}
                </div>
                <div className="text-gray-600">Bourses fermées</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main Content Area */}
          <div className="lg:w-2/3">
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Bourses de {decodedLevel} ({scholarships.length})
              </h2>
            </div>

            {/* Scholarships Grid */}
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                    <div className="aspect-[16/9] bg-gray-200"></div>
                    <div className="p-6">
                      <div className="h-5 bg-gray-200 rounded w-3/4 mb-3"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
                      <div className="h-4 bg-gray-200 rounded w-full mb-3"></div>
                      <div className="h-10 bg-gray-100 rounded w-full mt-4"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : scholarships.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📚</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Aucune bourse trouvée
                </h3>
                <p className="text-gray-600">
                  Aucune bourse n'est disponible pour ce niveau d'études actuellement.
                </p>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {scholarships.map((scholarship, index) => (
                    <EnhancedScholarshipCard
                      key={scholarship.id}
                      id={scholarship.id}
                      title={scholarship.title}
                      thumbnail={scholarship.thumbnail || ''}
                      deadline={scholarship.deadline}
                      isOpen={scholarship.isOpen}
                      level={scholarship.level}
                      country={scholarship.country}
                      onClick={handleScholarshipClick}
                      index={index}
                    />
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center mt-12">
                    <div className="flex space-x-2">
                      {[...Array(totalPages)].map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentPage(index + 1)}
                          className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                            currentPage === index + 1
                              ? `bg-${getLevelColor(decodedLevel)}-600 text-white`
                              : 'bg-white text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          {index + 1}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:w-1/3">
            {/* Other Levels */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Autres Niveaux d'Études
              </h3>
              <div className="space-y-2">
                {allLevels.filter(l => l !== decodedLevel).map((levelName) => (
                  <Link
                    key={levelName}
                    to={`/scholarships/level/${encodeURIComponent(levelName)}`}
                    className="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                  >
                    <span className="text-2xl mr-3">{getLevelIcon(levelName)}</span>
                    <span className="font-medium text-gray-900">{levelName}</span>
                  </Link>
                ))}
              </div>
            </div>

            {/* Latest Scholarships */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Dernières Bourses
              </h3>
              <div className="space-y-4">
                {latestScholarships.slice(0, 5).map((scholarship) => (
                  <div
                    key={scholarship.id}
                    onClick={() => handleScholarshipClick(scholarship.id)}
                    className="cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                  >
                    <h4 className="font-medium text-gray-900 text-sm mb-1 line-clamp-2">
                      {scholarship.title}
                    </h4>
                    <p className="text-xs text-gray-600 mb-2">
                      {scholarship.country} • {scholarship.level}
                    </p>
                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      scholarship.isOpen 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {scholarship.isOpen ? 'Ouvert' : 'Fermé'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScholarshipsByLevel;
