import React, { useState, useEffect } from 'react';
import { useParams, Link } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';
import ScholarshipCard from '../components/ScholarshipCard';

interface Scholarship {
  id: number;
  title: string;
  thumbnail?: string;
  deadline: string;
  isOpen: boolean;
  country: string;
  level?: string;
}

interface CountryStatistics {
  country: string;
  totalScholarships: number;
  openScholarships: number;
  closedScholarships: number;
  scholarshipsByLevel: Array<{
    level: string;
    count: number;
  }>;
}

const CountryDetail: React.FC = () => {
  const { country } = useParams<{ country: string }>();
  const { translations } = useLanguage();
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [allCountries, setAllCountries] = useState<string[]>([]);
  const [latestScholarships, setLatestScholarships] = useState<Scholarship[]>([]);
  const [statistics, setStatistics] = useState<CountryStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    level: '',
    isOpen: ''
  });

  const decodedCountry = country ? decodeURIComponent(country) : '';

  useEffect(() => {
    if (decodedCountry) {
      fetchScholarships();
      fetchStatistics();
      fetchAllCountries();
      fetchLatestScholarships();
    }
  }, [decodedCountry, currentPage, filters]);

  const fetchScholarships = async () => {
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12',
        ...filters
      });

      const response = await fetch(`/api/countries/${encodeURIComponent(decodedCountry)}/scholarships?${params}`);
      if (response.ok) {
        const data = await response.json();
        setScholarships(data.data.scholarships || []);
        setTotalPages(Math.ceil((data.data.scholarships?.length || 0) / 12));
      }
    } catch (error) {
      console.error('Error fetching scholarships:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await fetch(`/api/countries/${encodeURIComponent(decodedCountry)}/statistics`);
      if (response.ok) {
        const data = await response.json();
        setStatistics(data.data);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  const fetchAllCountries = async () => {
    try {
      const response = await fetch('/api/countries');
      if (response.ok) {
        const data = await response.json();
        setAllCountries(data.data?.map((item: any) => item.country) || []);
      }
    } catch (error) {
      console.error('Error fetching countries:', error);
    }
  };

  const fetchLatestScholarships = async () => {
    try {
      const response = await fetch('/api/scholarships?limit=6&orderBy=created_at&orderDirection=DESC');
      if (response.ok) {
        const data = await response.json();
        setLatestScholarships(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching latest scholarships:', error);
    }
  };

  const handleScholarshipClick = (id: number) => {
    window.location.href = `/scholarships/${id}`;
  };

  const getCountryFlag = (countryName: string): string => {
    const flags: { [key: string]: string } = {
      'Australia': '🇦🇺',
      'Brazil': '🇧🇷',
      'Canada': '🇨🇦',
      'France': '🇫🇷',
      'Germany': '🇩🇪',
      'Japan': '🇯🇵',
      'United States': '🇺🇸',
      'United Kingdom': '🇬🇧',
      'China': '🇨🇳',
      'India': '🇮🇳',
      'South Korea': '🇰🇷',
      'Netherlands': '🇳🇱',
      'Sweden': '🇸🇪',
      'Switzerland': '🇨🇭',
      'Norway': '🇳🇴'
    };
    return flags[countryName] || '🌍';
  };



  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Chargement des bourses...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center mb-6">
            <Link
              to="/countries"
              className="flex items-center text-blue-200 hover:text-white transition-colors duration-200"
            >
              <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Retour aux pays
            </Link>
          </div>
          
          <div className="flex items-center mb-4">
            <div className="text-6xl mr-4">
              {getCountryFlag(decodedCountry)}
            </div>
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-2">
                {decodedCountry}
              </h1>
              <p className="text-xl text-blue-100">
                Bourses d'études disponibles
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Section */}
      {statistics && (
        <div className="bg-white py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {statistics.totalScholarships}
                </div>
                <div className="text-gray-600">Total des bourses</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {statistics.openScholarships}
                </div>
                <div className="text-gray-600">Bourses ouvertes</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600 mb-2">
                  {statistics.closedScholarships}
                </div>
                <div className="text-gray-600">Bourses fermées</div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  {statistics.scholarshipsByLevel.length}
                </div>
                <div className="text-gray-600">Niveaux disponibles</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content with Sidebar */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main Content Area */}
          <div className="lg:w-2/3">
            {/* Filters Section */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Filtres</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Niveau d'études
                  </label>
                  <select
                    value={filters.level}
                    onChange={(e) => setFilters({ ...filters, level: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Tous les niveaux</option>
                    <option value="Bachelor">Licence</option>
                    <option value="Master">Master</option>
                    <option value="PhD">Doctorat</option>
                    <option value="Postdoc">Post-doctorat</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Statut
                  </label>
                  <select
                    value={filters.isOpen}
                    onChange={(e) => setFilters({ ...filters, isOpen: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Toutes les bourses</option>
                    <option value="true">Ouvertes</option>
                    <option value="false">Fermées</option>
                  </select>
                </div>

                <div className="flex items-end">
                  <button
                    onClick={() => {
                      setFilters({ level: '', isOpen: '' });
                      setCurrentPage(1);
                    }}
                    className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200"
                  >
                    Réinitialiser
                  </button>
                </div>
              </div>
            </div>

            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Bourses en {decodedCountry} ({scholarships.length})
              </h2>
            </div>

            {/* Scholarships Grid */}
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                    <div className="aspect-[16/9] bg-gray-200"></div>
                    <div className="p-6">
                      <div className="h-5 bg-gray-200 rounded w-3/4 mb-3"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
                      <div className="h-4 bg-gray-200 rounded w-full mb-3"></div>
                      <div className="h-10 bg-gray-100 rounded w-full mt-4"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : scholarships.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📚</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {translations.countries.noScholarships}
                </h3>
                <p className="text-gray-600">
                  Aucune bourse n'est actuellement disponible pour ce pays avec les filtres sélectionnés.
                </p>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {scholarships.map((scholarship) => (
                    <ScholarshipCard
                      key={scholarship.id}
                      id={scholarship.id}
                      title={scholarship.title}
                      thumbnail={scholarship.thumbnail || ''}
                      deadline={scholarship.deadline}
                      isOpen={scholarship.isOpen}
                      country={scholarship.country}
                      onClick={handleScholarshipClick}
                    />
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center mt-12">
                    <div className="flex space-x-2">
                      {[...Array(totalPages)].map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentPage(index + 1)}
                          className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                            currentPage === index + 1
                              ? 'bg-blue-600 text-white'
                              : 'bg-white text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          {index + 1}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:w-1/3">
            {/* Other Countries */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Autres Pays
              </h3>
              <div className="space-y-2 max-h-80 overflow-y-auto">
                {allCountries.filter(c => c !== decodedCountry).slice(0, 15).map((countryName) => (
                  <Link
                    key={countryName}
                    to={`/countries/${encodeURIComponent(countryName)}`}
                    className="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                  >
                    <span className="text-2xl mr-3">{getCountryFlag(countryName)}</span>
                    <span className="font-medium text-gray-900">{countryName}</span>
                  </Link>
                ))}
              </div>
              <div className="mt-4 pt-4 border-t">
                <Link
                  to="/countries"
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  Voir tous les pays →
                </Link>
              </div>
            </div>

            {/* Latest Scholarships */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Dernières Bourses
              </h3>
              <div className="space-y-4">
                {latestScholarships.slice(0, 5).map((scholarship) => (
                  <div
                    key={scholarship.id}
                    onClick={() => handleScholarshipClick(scholarship.id)}
                    className="cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                  >
                    <h4 className="font-medium text-gray-900 text-sm mb-1 line-clamp-2">
                      {scholarship.title}
                    </h4>
                    <p className="text-xs text-gray-600 mb-2">
                      {scholarship.country} • {scholarship.level}
                    </p>
                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      scholarship.isOpen
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {scholarship.isOpen ? 'Ouvert' : 'Fermé'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CountryDetail;
