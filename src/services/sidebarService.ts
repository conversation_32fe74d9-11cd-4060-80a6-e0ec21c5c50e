/**
 * Professional Sidebar Data Service
 * Unified service for fetching sidebar data across all dedicated pages
 * Following industry standards like greatyop.com
 */

export interface SidebarData {
  relatedItems: any[];
  latestItems: any[];
  statistics?: any;
}

export interface SidebarConfig {
  type: 'countries' | 'levels' | 'opportunities';
  currentItem?: string;
  excludeId?: number;
  limit?: number;
}

class SidebarService {
  private baseUrl = '/api';

  /**
   * Fetch unified sidebar data for any page type
   */
  async fetchSidebarData(config: SidebarConfig): Promise<SidebarData> {
    try {
      const promises = [];

      // Fetch related items based on page type
      promises.push(this.fetchRelatedItems(config));
      
      // Fetch latest items (scholarships or opportunities)
      promises.push(this.fetchLatestItems(config));

      // Fetch statistics if needed
      if (config.currentItem) {
        promises.push(this.fetchStatistics(config));
      }

      const [relatedItems, latestItems, statistics] = await Promise.all(promises);

      return {
        relatedItems: relatedItems || [],
        latestItems: latestItems || [],
        statistics
      };
    } catch (error) {
      console.error('Error fetching sidebar data:', error);
      return {
        relatedItems: [],
        latestItems: []
      };
    }
  }

  /**
   * Fetch related items (countries, levels, or opportunity types)
   */
  private async fetchRelatedItems(config: SidebarConfig): Promise<any[]> {
    try {
      let endpoint = '';
      const params = new URLSearchParams();

      if (config.limit) {
        params.append('limit', config.limit.toString());
      }

      if (config.currentItem) {
        params.append(`exclude${this.getExcludeParam(config.type)}`, config.currentItem);
      }

      switch (config.type) {
        case 'countries':
          endpoint = `${this.baseUrl}/countries/sidebar`;
          break;
        case 'levels':
          endpoint = `${this.baseUrl}/scholarships/levels`;
          break;
        case 'opportunities':
          endpoint = `${this.baseUrl}/opportunities/types-sidebar`;
          break;
      }

      const response = await fetch(`${endpoint}?${params}`);
      if (response.ok) {
        const data = await response.json();
        return data.data || [];
      }
      return [];
    } catch (error) {
      console.error(`Error fetching related ${config.type}:`, error);
      return [];
    }
  }

  /**
   * Fetch latest items (scholarships or opportunities)
   */
  private async fetchLatestItems(config: SidebarConfig): Promise<any[]> {
    try {
      const params = new URLSearchParams();
      params.append('limit', '6');

      if (config.excludeId) {
        params.append('excludeId', config.excludeId.toString());
      }

      let endpoint = '';
      if (config.type === 'opportunities') {
        endpoint = `${this.baseUrl}/opportunities/latest`;
      } else {
        endpoint = `${this.baseUrl}/scholarships/latest`;
      }

      const response = await fetch(`${endpoint}?${params}`);
      if (response.ok) {
        const data = await response.json();
        return data.data || [];
      }
      return [];
    } catch (error) {
      console.error('Error fetching latest items:', error);
      return [];
    }
  }

  /**
   * Fetch statistics for current item
   */
  private async fetchStatistics(config: SidebarConfig): Promise<any> {
    if (!config.currentItem) return null;

    try {
      let endpoint = '';
      
      switch (config.type) {
        case 'countries':
          endpoint = `${this.baseUrl}/countries/${encodeURIComponent(config.currentItem)}/statistics`;
          break;
        case 'levels':
          // Level statistics can be computed from the main data
          return null;
        case 'opportunities':
          // Opportunity type statistics can be computed from the main data
          return null;
      }

      if (endpoint) {
        const response = await fetch(endpoint);
        if (response.ok) {
          const data = await response.json();
          return data.data;
        }
      }
      return null;
    } catch (error) {
      console.error('Error fetching statistics:', error);
      return null;
    }
  }

  /**
   * Get the correct exclude parameter name based on type
   */
  private getExcludeParam(type: string): string {
    switch (type) {
      case 'countries':
        return 'Country';
      case 'levels':
        return 'Level';
      case 'opportunities':
        return 'Type';
      default:
        return 'Item';
    }
  }

  /**
   * Get country flag emoji (utility function)
   */
  getCountryFlag(country: string): string {
    const flagMap: { [key: string]: string } = {
      'France': '🇫🇷',
      'Germany': '🇩🇪',
      'United Kingdom': '🇬🇧',
      'United States': '🇺🇸',
      'Canada': '🇨🇦',
      'Australia': '🇦🇺',
      'Netherlands': '🇳🇱',
      'Sweden': '🇸🇪',
      'Norway': '🇳🇴',
      'Denmark': '🇩🇰',
      'Switzerland': '🇨🇭',
      'Belgium': '🇧🇪',
      'Austria': '🇦🇹',
      'Italy': '🇮🇹',
      'Spain': '🇪🇸',
      'Japan': '🇯🇵',
      'South Korea': '🇰🇷',
      'Singapore': '🇸🇬',
      'New Zealand': '🇳🇿',
      'Finland': '🇫🇮'
    };
    return flagMap[country] || '🌍';
  }

  /**
   * Get level icon (utility function)
   */
  getLevelIcon(level: string): string {
    const iconMap: { [key: string]: string } = {
      'Bachelor': '🎓',
      'Master': '📚',
      'PhD': '🔬',
      'Postdoc': '👨‍🔬',
      'Undergraduate': '🎓',
      'Graduate': '📚',
      'Doctorate': '🔬'
    };
    return iconMap[level] || '📖';
  }

  /**
   * Get opportunity type icon (utility function)
   */
  getOpportunityIcon(type: string): string {
    const iconMap: { [key: string]: string } = {
      'internship': '💼',
      'training': '🎯',
      'conference': '🎤',
      'workshop': '🛠️',
      'competition': '🏆'
    };
    return iconMap[type] || '🔗';
  }
}

export const sidebarService = new SidebarService();
export default sidebarService;
