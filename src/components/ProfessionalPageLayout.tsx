import React from 'react';
import ProfessionalSidebar from './ProfessionalSidebar';
import { SidebarConfig } from '../services/sidebarService';

interface HeroSection {
  title: string;
  subtitle?: string;
  icon?: string;
  backgroundColor?: string;
  textColor?: string;
}

interface StatisticsData {
  total: number;
  active: number;
  inactive?: number;
  label: string;
  activeLabel: string;
  inactiveLabel?: string;
}

interface FilterOption {
  key: string;
  label: string;
  value: string;
  options: Array<{ value: string; label: string }>;
}

interface ProfessionalPageLayoutProps {
  hero: HeroSection;
  statistics?: StatisticsData;
  filters?: FilterOption[];
  onFilterChange?: (filters: Record<string, string>) => void;
  sidebarConfig: SidebarConfig;
  children: React.ReactNode;
  loading?: boolean;
  className?: string;
}

const ProfessionalPageLayout: React.FC<ProfessionalPageLayoutProps> = ({
  hero,
  statistics,
  filters = [],
  onFilterChange,
  sidebarConfig,
  children,
  loading = false,
  className = ''
}) => {
  const [currentFilters, setCurrentFilters] = React.useState<Record<string, string>>({});

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...currentFilters, [key]: value };
    setCurrentFilters(newFilters);
    onFilterChange?.(newFilters);
  };

  const getHeroBackgroundClass = () => {
    if (hero.backgroundColor) {
      return hero.backgroundColor;
    }
    
    // Default colors based on page type
    switch (sidebarConfig.type) {
      case 'countries':
        return 'bg-gradient-to-r from-blue-600 to-blue-800';
      case 'levels':
        return 'bg-gradient-to-r from-green-600 to-green-800';
      case 'opportunities':
        return 'bg-gradient-to-r from-purple-600 to-purple-800';
      default:
        return 'bg-gradient-to-r from-gray-600 to-gray-800';
    }
  };

  const renderHeroSection = () => (
    <div className={`${getHeroBackgroundClass()} ${hero.textColor || 'text-white'}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          {hero.icon && (
            <div className="text-6xl mb-4">{hero.icon}</div>
          )}
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            {hero.title}
          </h1>
          {hero.subtitle && (
            <p className="text-xl opacity-90 max-w-3xl mx-auto">
              {hero.subtitle}
            </p>
          )}
        </div>
      </div>
    </div>
  );

  const renderStatistics = () => {
    if (!statistics) return null;

    return (
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">{statistics.total}</div>
              <div className="text-gray-600">{statistics.label}</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">{statistics.active}</div>
              <div className="text-gray-600">{statistics.activeLabel}</div>
            </div>
            {statistics.inactive !== undefined && statistics.inactiveLabel && (
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600">{statistics.inactive}</div>
                <div className="text-gray-600">{statistics.inactiveLabel}</div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderFilters = () => {
    if (filters.length === 0) return null;

    return (
      <div className="bg-gray-50 border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-wrap gap-4">
            {filters.map((filter) => (
              <div key={filter.key} className="min-w-0 flex-1 md:flex-none md:w-48">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {filter.label}
                </label>
                <select
                  value={currentFilters[filter.key] || ''}
                  onChange={(e) => handleFilterChange(filter.key, e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Tous</option>
                  {filter.options.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderMainContent = () => (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Main Content */}
        <div className="lg:w-2/3">
          {children}
        </div>

        {/* Sidebar */}
        <ProfessionalSidebar config={sidebarConfig} />
      </div>
    </div>
  );

  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      {/* Hero Section */}
      {renderHeroSection()}

      {/* Statistics Section */}
      {renderStatistics()}

      {/* Filters Section */}
      {renderFilters()}

      {/* Main Content with Sidebar */}
      {renderMainContent()}
    </div>
  );
};

/**
 * Professional Content Grid Component
 * Unified grid for displaying scholarships or opportunities
 */
interface ContentGridProps {
  items: any[];
  loading: boolean;
  emptyMessage: string;
  emptyIcon: string;
  renderItem: (item: any) => React.ReactNode;
  pagination?: {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
  };
}

export const ProfessionalContentGrid: React.FC<ContentGridProps> = ({
  items,
  loading,
  emptyMessage,
  emptyIcon,
  renderItem,
  pagination
}) => {
  const renderLoadingGrid = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {[...Array(6)].map((_, index) => (
        <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
          <div className="aspect-[16/9] bg-gray-200"></div>
          <div className="p-6">
            <div className="h-5 bg-gray-200 rounded w-3/4 mb-3"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
            <div className="h-4 bg-gray-200 rounded w-full mb-3"></div>
            <div className="h-10 bg-gray-100 rounded w-full mt-4"></div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderEmptyState = () => (
    <div className="text-center py-12">
      <div className="text-6xl mb-4">{emptyIcon}</div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">
        Aucun résultat trouvé
      </h3>
      <p className="text-gray-600">
        {emptyMessage}
      </p>
    </div>
  );

  const renderPagination = () => {
    if (!pagination || pagination.totalPages <= 1) return null;

    const { currentPage, totalPages, onPageChange } = pagination;
    const pages = [];

    // Add page numbers
    for (let i = 1; i <= totalPages; i++) {
      if (
        i === 1 ||
        i === totalPages ||
        (i >= currentPage - 2 && i <= currentPage + 2)
      ) {
        pages.push(i);
      } else if (
        i === currentPage - 3 ||
        i === currentPage + 3
      ) {
        pages.push('...');
      }
    }

    return (
      <div className="flex justify-center items-center space-x-2 mt-8">
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="px-3 py-2 rounded-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Précédent
        </button>

        {pages.map((page, index) => (
          <button
            key={index}
            onClick={() => typeof page === 'number' ? onPageChange(page) : undefined}
            disabled={typeof page !== 'number'}
            className={`px-3 py-2 rounded-md border ${
              page === currentPage
                ? 'border-blue-500 bg-blue-500 text-white'
                : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
            } ${typeof page !== 'number' ? 'cursor-default' : 'cursor-pointer'}`}
          >
            {page}
          </button>
        ))}

        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="px-3 py-2 rounded-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Suivant
        </button>
      </div>
    );
  };

  if (loading) {
    return renderLoadingGrid();
  }

  if (items.length === 0) {
    return renderEmptyState();
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {items.map(renderItem)}
      </div>
      {renderPagination()}
    </>
  );
};

export default ProfessionalPageLayout;
