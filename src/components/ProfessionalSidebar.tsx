import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';
import sidebarService, { SidebarConfig, SidebarData } from '../services/sidebarService';

interface ProfessionalSidebarProps {
  config: SidebarConfig;
  className?: string;
}

interface SidebarItem {
  id?: number;
  name: string;
  title?: string;
  totalCount?: number;
  openCount?: number;
  activeCount?: number;
  count?: number;
  slug?: string;
  country?: string;
  level?: string;
  type?: string;
  organization?: string;
  deadline?: string;
  isOpen?: boolean;
  isActive?: boolean;
  isExpired?: boolean;
  daysRemaining?: number;
}

const ProfessionalSidebar: React.FC<ProfessionalSidebarProps> = ({ config, className = '' }) => {
  const { translations } = useLanguage();
  const [sidebarData, setSidebarData] = useState<SidebarData>({
    relatedItems: [],
    latestItems: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSidebarData();
  }, [config]);

  const fetchSidebarData = async () => {
    try {
      setLoading(true);
      const data = await sidebarService.fetchSidebarData(config);
      setSidebarData(data);
    } catch (error) {
      console.error('Error fetching sidebar data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRelatedItemsTitle = () => {
    switch (config.type) {
      case 'countries':
        return 'Autres Pays';
      case 'levels':
        return 'Autres Niveaux d\'Études';
      case 'opportunities':
        return 'Autres Types d\'Opportunités';
      default:
        return 'Éléments Connexes';
    }
  };

  const getLatestItemsTitle = () => {
    return config.type === 'opportunities' ? 'Dernières Opportunités' : 'Dernières Bourses';
  };

  const getRelatedItemLink = (item: SidebarItem) => {
    switch (config.type) {
      case 'countries':
        return `/countries/${encodeURIComponent(item.name)}`;
      case 'levels':
        return `/scholarships/level/${encodeURIComponent(item.name)}`;
      case 'opportunities':
        return `/opportunities/type/${encodeURIComponent(item.name)}`;
      default:
        return '#';
    }
  };

  const getRelatedItemIcon = (item: SidebarItem) => {
    switch (config.type) {
      case 'countries':
        return sidebarService.getCountryFlag(item.name);
      case 'levels':
        return sidebarService.getLevelIcon(item.name);
      case 'opportunities':
        return sidebarService.getOpportunityIcon(item.name);
      default:
        return '📄';
    }
  };

  const getLatestItemLink = (item: SidebarItem) => {
    if (config.type === 'opportunities') {
      return `/opportunities/${item.id}`;
    }
    return `/scholarships/${item.id}`;
  };

  const renderRelatedItems = () => {
    if (loading) {
      return (
        <div className="space-y-2">
          {[...Array(8)].map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="flex items-center p-3 rounded-lg">
                <div className="w-8 h-8 bg-gray-200 rounded mr-3"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      );
    }

    return (
      <div className="space-y-2 max-h-80 overflow-y-auto">
        {sidebarData.relatedItems.slice(0, 15).map((item: SidebarItem, index) => (
          <Link
            key={index}
            to={getRelatedItemLink(item)}
            className="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 group"
          >
            <span className="text-2xl mr-3 group-hover:scale-110 transition-transform duration-200">
              {getRelatedItemIcon(item)}
            </span>
            <div className="flex-1 min-w-0">
              <div className="font-medium text-gray-900 truncate">{item.name}</div>
              <div className="text-xs text-gray-500">
                {item.totalCount || item.count} {config.type === 'opportunities' ? 'opportunités' : 'bourses'}
                {(item.openCount || item.activeCount) && (
                  <span className="text-green-600 ml-1">
                    • {item.openCount || item.activeCount} {config.type === 'opportunities' ? 'actives' : 'ouvertes'}
                  </span>
                )}
              </div>
            </div>
          </Link>
        ))}
      </div>
    );
  };

  const renderLatestItems = () => {
    if (loading) {
      return (
        <div className="space-y-4">
          {[...Array(5)].map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="p-3 rounded-lg">
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3 mb-2"></div>
                <div className="h-6 bg-gray-200 rounded w-16"></div>
              </div>
            </div>
          ))}
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {sidebarData.latestItems.slice(0, 5).map((item: SidebarItem) => (
          <Link
            key={item.id}
            to={getLatestItemLink(item)}
            className="block p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            <h4 className="font-medium text-gray-900 text-sm mb-1 line-clamp-2">
              {item.title}
            </h4>
            <p className="text-xs text-gray-600 mb-2">
              {config.type === 'opportunities' 
                ? `${item.organization} • ${item.type}`
                : `${item.country} • ${item.level}`
              }
            </p>
            <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              (config.type === 'opportunities' ? item.isActive : item.isOpen)
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {(config.type === 'opportunities' ? item.isActive : item.isOpen) 
                ? (config.type === 'opportunities' ? 'Active' : 'Ouverte')
                : (config.type === 'opportunities' ? 'Inactive' : 'Fermée')
              }
            </div>
          </Link>
        ))}
      </div>
    );
  };

  return (
    <div className={`lg:w-1/3 ${className}`}>
      {/* Related Items Section */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <span className="mr-2">🔗</span>
          {getRelatedItemsTitle()}
        </h3>
        {renderRelatedItems()}
      </div>

      {/* Latest Items Section */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <span className="mr-2">⭐</span>
          {getLatestItemsTitle()}
        </h3>
        {renderLatestItems()}
      </div>
    </div>
  );
};

export default ProfessionalSidebar;
