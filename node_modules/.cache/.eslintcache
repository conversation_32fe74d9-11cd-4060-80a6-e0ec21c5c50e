[{"/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx": "1", "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx": "2", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx": "3", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx": "4", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx": "5", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx": "6", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx": "7", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx": "8", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx": "9", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx": "10", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx": "11", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx": "12", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx": "13", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx": "14", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx": "15", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts": "16", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts": "17", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx": "18", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx": "19", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts": "20", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts": "21", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts": "22", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx": "23", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx": "24", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx": "25", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx": "26", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx": "27", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx": "28", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx": "29", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx": "30", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx": "31", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx": "32", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx": "33", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx": "34", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx": "35", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx": "36", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx": "37", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx": "38", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx": "39", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx": "40", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx": "41", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx": "42", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx": "43", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx": "44", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts": "45", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts": "46", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts": "47", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx": "48", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx": "49", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx": "50", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx": "51", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx": "52", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx": "53", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts": "54", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx": "55", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx": "56", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx": "57", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx": "58", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx": "59", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx": "60", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx": "61", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx": "62", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx": "63", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx": "64", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx": "65", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx": "66", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx": "67", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx": "68", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx": "69", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts": "70", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx": "71", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx": "72", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx": "73", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx": "74", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx": "75", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts": "76", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts": "77", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx": "78", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx": "79", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx": "80", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx": "81", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx": "82", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx": "83", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx": "84", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx": "85", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx": "86", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx": "87", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx": "88", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx": "89", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx": "90", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx": "91", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx": "92", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx": "93", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx": "94", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx": "95", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx": "96", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx": "97", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx": "98", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx": "99", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx": "100", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx": "101", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts": "102", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx": "103", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts": "104", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts": "105", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx": "106", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx": "107", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx": "108", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx": "109", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx": "110", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx": "111", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx": "112", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx": "113", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx": "114", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx": "115", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx": "116", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts": "117", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx": "118", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts": "119", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx": "120", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx": "121", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx": "122", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx": "123", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx": "124", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx": "125", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx": "126", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx": "127", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx": "128", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx": "129", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx": "130", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx": "131", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx": "132", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx": "133", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx": "134", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx": "135", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx": "136", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx": "137", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts": "138", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts": "139", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts": "140", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts": "141", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx": "142", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx": "143", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx": "144", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts": "145", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts": "146", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/AuthContext.tsx": "147", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/authService.ts": "148", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProtectedRoute.tsx": "149", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminLogin.tsx": "150", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/ScholarshipManager.tsx": "151", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/MessagesManager.tsx": "152", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/ScholarshipForm.tsx": "153", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Countries.tsx": "154", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Guides.tsx": "155", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Opportunities.tsx": "156", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx": "157", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/GuideManager.tsx": "158", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/OpportunityManager.tsx": "159", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/imageUtils.ts": "160", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/EnhancedHeader.tsx": "161", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx": "162", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Dropdown.tsx": "163", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/MobileNavigationDropdown.tsx": "164", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/ScholarshipsByLevel.tsx": "165", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/OpportunitiesByType.tsx": "166", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/sidebarService.ts": "167", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalPageLayout.tsx": "168", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalSidebar.tsx": "169", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dataPrefetcher.ts": "170"}, {"size": 274, "mtime": 1745947759865, "results": "171", "hashOfConfig": "172"}, {"size": 2118, "mtime": 1745983514069, "results": "173", "hashOfConfig": "172"}, {"size": 5438, "mtime": 1745977740794, "results": "174", "hashOfConfig": "172"}, {"size": 10242, "mtime": 1745981563339, "results": "175", "hashOfConfig": "172"}, {"size": 1742, "mtime": 1745977713025, "results": "176", "hashOfConfig": "172"}, {"size": 7131, "mtime": 1745981562258, "results": "177", "hashOfConfig": "172"}, {"size": 10498, "mtime": 1745982258490, "results": "178", "hashOfConfig": "172"}, {"size": 3076, "mtime": 1745945973317, "results": "179", "hashOfConfig": "172"}, {"size": 553, "mtime": 1745978322072, "results": "180", "hashOfConfig": "172"}, {"size": 6250, "mtime": 1745977773426, "results": "181", "hashOfConfig": "172"}, {"size": 2446, "mtime": 1745945132621, "results": "182", "hashOfConfig": "172"}, {"size": 2645, "mtime": 1745977581979, "results": "183", "hashOfConfig": "172"}, {"size": 4167, "mtime": 1745981843303, "results": "184", "hashOfConfig": "172"}, {"size": 5713, "mtime": 1745981758114, "results": "185", "hashOfConfig": "172"}, {"size": 675, "mtime": 1745976791748, "results": "186", "hashOfConfig": "172"}, {"size": 1059, "mtime": 1745976720607, "results": "187", "hashOfConfig": "172"}, {"size": 3452, "mtime": 1745946003719, "results": "188", "hashOfConfig": "172"}, {"size": 2518, "mtime": 1745983866923, "results": "189", "hashOfConfig": "172"}, {"size": 1737, "mtime": 1745978376608, "results": "190", "hashOfConfig": "172"}, {"size": 4075, "mtime": 1745982269507, "results": "191", "hashOfConfig": "172"}, {"size": 4531, "mtime": 1745982263875, "results": "192", "hashOfConfig": "172"}, {"size": 5406, "mtime": 1745982274929, "results": "193", "hashOfConfig": "172"}, {"size": 2535, "mtime": 1745978386143, "results": "194", "hashOfConfig": "172"}, {"size": 737, "mtime": 1745944438688, "results": "195", "hashOfConfig": "172"}, {"size": 2323, "mtime": 1745982233889, "results": "196", "hashOfConfig": "172"}, {"size": 274, "mtime": 1745947759865, "results": "197", "hashOfConfig": "198"}, {"size": 4324, "mtime": 1746276088446, "results": "199", "hashOfConfig": "198"}, {"size": 7131, "mtime": 1745981562258, "results": "200", "hashOfConfig": "198"}, {"size": 10498, "mtime": 1745982258490, "results": "201", "hashOfConfig": "198"}, {"size": 10242, "mtime": 1745981563339, "results": "202", "hashOfConfig": "198"}, {"size": 1742, "mtime": 1745977713025, "results": "203", "hashOfConfig": "198"}, {"size": 8315, "mtime": 1746204095547, "results": "204", "hashOfConfig": "198"}, {"size": 3076, "mtime": 1745945973317, "results": "205", "hashOfConfig": "198"}, {"size": 2535, "mtime": 1746033992389, "results": "206", "hashOfConfig": "198"}, {"size": 1737, "mtime": 1745978376608, "results": "207", "hashOfConfig": "198"}, {"size": 6250, "mtime": 1745977773426, "results": "208", "hashOfConfig": "198"}, {"size": 553, "mtime": 1745978322072, "results": "209", "hashOfConfig": "198"}, {"size": 2446, "mtime": 1745945132621, "results": "210", "hashOfConfig": "198"}, {"size": 5154, "mtime": 1746276100698, "results": "211", "hashOfConfig": "198"}, {"size": 8097, "mtime": 1745983506688, "results": "212", "hashOfConfig": "198"}, {"size": 6024, "mtime": 1746026061709, "results": "213", "hashOfConfig": "198"}, {"size": 9559, "mtime": 1746270350218, "results": "214", "hashOfConfig": "198"}, {"size": 737, "mtime": 1745944438688, "results": "215", "hashOfConfig": "198"}, {"size": 2645, "mtime": 1745977581979, "results": "216", "hashOfConfig": "198"}, {"size": 4531, "mtime": 1745982263875, "results": "217", "hashOfConfig": "198"}, {"size": 4075, "mtime": 1745982269507, "results": "218", "hashOfConfig": "198"}, {"size": 5406, "mtime": 1745982274929, "results": "219", "hashOfConfig": "198"}, {"size": 5713, "mtime": 1745981758114, "results": "220", "hashOfConfig": "198"}, {"size": 675, "mtime": 1745976791748, "results": "221", "hashOfConfig": "198"}, {"size": 4167, "mtime": 1745981843303, "results": "222", "hashOfConfig": "198"}, {"size": 2115, "mtime": 1746029576846, "results": "223", "hashOfConfig": "198"}, {"size": 1689, "mtime": 1745982730905, "results": "224", "hashOfConfig": "198"}, {"size": 19288, "mtime": 1745983400384, "results": "225", "hashOfConfig": "198"}, {"size": 1059, "mtime": 1745976720607, "results": "226", "hashOfConfig": "198"}, {"size": 2535, "mtime": 1745978386143, "results": "227", "hashOfConfig": "198"}, {"size": 14052, "mtime": 1746274142489, "results": "228", "hashOfConfig": "198"}, {"size": 3939, "mtime": 1746017528736, "results": "229", "hashOfConfig": "198"}, {"size": 13857, "mtime": 1746282401482, "results": "230", "hashOfConfig": "198"}, {"size": 11940, "mtime": 1746252382064, "results": "231", "hashOfConfig": "198"}, {"size": 38877, "mtime": 1746252407519, "results": "232", "hashOfConfig": "198"}, {"size": 8393, "mtime": 1746249939564, "results": "233", "hashOfConfig": "198"}, {"size": 11544, "mtime": 1746272209267, "results": "234", "hashOfConfig": "198"}, {"size": 1343, "mtime": 1746033436995, "results": "235", "hashOfConfig": "198"}, {"size": 1752, "mtime": 1746274101102, "results": "236", "hashOfConfig": "198"}, {"size": 3341, "mtime": 1746199132190, "results": "237", "hashOfConfig": "198"}, {"size": 7045, "mtime": 1746199160974, "results": "238", "hashOfConfig": "198"}, {"size": 4103, "mtime": 1746200520123, "results": "239", "hashOfConfig": "198"}, {"size": 6276, "mtime": 1746249196201, "results": "240", "hashOfConfig": "198"}, {"size": 7222, "mtime": 1746249216418, "results": "241", "hashOfConfig": "198"}, {"size": 1105, "mtime": 1746201832350, "results": "242", "hashOfConfig": "198"}, {"size": 921, "mtime": 1746202207790, "results": "243", "hashOfConfig": "198"}, {"size": 11379, "mtime": 1746276323665, "results": "244", "hashOfConfig": "198"}, {"size": 1059, "mtime": 1746226321253, "results": "245", "hashOfConfig": "198"}, {"size": 7914, "mtime": 1746251582912, "results": "246", "hashOfConfig": "198"}, {"size": 4784, "mtime": 1746252717773, "results": "247", "hashOfConfig": "198"}, {"size": 1777, "mtime": 1746254015165, "results": "248", "hashOfConfig": "198"}, {"size": 9435, "mtime": 1746252638103, "results": "249", "hashOfConfig": "198"}, {"size": 5504, "mtime": 1746275135511, "results": "250", "hashOfConfig": "198"}, {"size": 1053, "mtime": 1752353080389, "results": "251", "hashOfConfig": "252"}, {"size": 5424, "mtime": 1752381172912, "results": "253", "hashOfConfig": "252"}, {"size": 13828, "mtime": 1752248890943, "results": "254", "hashOfConfig": "252"}, {"size": 19195, "mtime": 1747280789154, "results": "255", "hashOfConfig": "252"}, {"size": 3587, "mtime": 1747235972243, "results": "256", "hashOfConfig": "252"}, {"size": 10653, "mtime": 1747235897205, "results": "257", "hashOfConfig": "252"}, {"size": 3076, "mtime": 1745945973317, "results": "258", "hashOfConfig": "252"}, {"size": 1974, "mtime": 1747278879917, "results": "259", "hashOfConfig": "252"}, {"size": 577, "mtime": 1752352136699, "results": "260", "hashOfConfig": "252"}, {"size": 18635, "mtime": 1747235596822, "results": "261", "hashOfConfig": "262"}, {"size": 2446, "mtime": 1745945132621, "results": "263", "hashOfConfig": "252"}, {"size": 19874, "mtime": 1752251800113, "results": "264", "hashOfConfig": "252"}, {"size": 5624, "mtime": 1752347411216, "results": "265", "hashOfConfig": "252"}, {"size": 11326, "mtime": 1752283692783, "results": "266", "hashOfConfig": "252"}, {"size": 14078, "mtime": 1752283590200, "results": "267", "hashOfConfig": "252"}, {"size": 12834, "mtime": 1752281264858, "results": "268", "hashOfConfig": "252"}, {"size": 7045, "mtime": 1746199160974, "results": "269", "hashOfConfig": "252"}, {"size": 3341, "mtime": 1746199132190, "results": "270", "hashOfConfig": "252"}, {"size": 1059, "mtime": 1746226321253, "results": "271", "hashOfConfig": "252"}, {"size": 6276, "mtime": 1746249196201, "results": "272", "hashOfConfig": "252"}, {"size": 921, "mtime": 1746202207790, "results": "273", "hashOfConfig": "252"}, {"size": 1695, "mtime": 1747186871230, "results": "274", "hashOfConfig": "262"}, {"size": 3161, "mtime": 1747232764014, "results": "275", "hashOfConfig": "262"}, {"size": 7071, "mtime": 1752353425661, "results": "276", "hashOfConfig": "252"}, {"size": 12973, "mtime": 1747224498475, "results": "277", "hashOfConfig": "252"}, {"size": 6490, "mtime": 1752353387940, "results": "278", "hashOfConfig": "252"}, {"size": 8394, "mtime": 1752353443428, "results": "279", "hashOfConfig": "252"}, {"size": 8023, "mtime": 1752346082272, "results": "280", "hashOfConfig": "252"}, {"size": 675, "mtime": 1745976791748, "results": "281", "hashOfConfig": "262"}, {"size": 7860, "mtime": 1752251788545, "results": "282", "hashOfConfig": "252"}, {"size": 7222, "mtime": 1746249216418, "results": "283", "hashOfConfig": "252"}, {"size": 9917, "mtime": 1752283723085, "results": "284", "hashOfConfig": "252"}, {"size": 5948, "mtime": 1752352806967, "results": "285", "hashOfConfig": "252"}, {"size": 2535, "mtime": 1745978386143, "results": "286", "hashOfConfig": "252"}, {"size": 3877, "mtime": 1747235933700, "results": "287", "hashOfConfig": "252"}, {"size": 1689, "mtime": 1745982730905, "results": "288", "hashOfConfig": "252"}, {"size": 3697, "mtime": 1747184461868, "results": "289", "hashOfConfig": "262"}, {"size": 959, "mtime": 1747186815101, "results": "290", "hashOfConfig": "262"}, {"size": 5906, "mtime": 1752248869494, "results": "291", "hashOfConfig": "252"}, {"size": 2847, "mtime": 1747187027857, "results": "292", "hashOfConfig": "252"}, {"size": 2604, "mtime": 1747279467729, "results": "293", "hashOfConfig": "252"}, {"size": 8119, "mtime": 1747220020952, "results": "294", "hashOfConfig": "262"}, {"size": 8243, "mtime": 1747220059414, "results": "295", "hashOfConfig": "262"}, {"size": 3071, "mtime": 1747221577347, "results": "296", "hashOfConfig": "262"}, {"size": 6125, "mtime": 1747221750779, "results": "297", "hashOfConfig": "262"}, {"size": 6017, "mtime": 1747221715802, "results": "298", "hashOfConfig": "262"}, {"size": 3890, "mtime": 1747221780672, "results": "299", "hashOfConfig": "262"}, {"size": 3377, "mtime": 1747221613654, "results": "300", "hashOfConfig": "262"}, {"size": 3156, "mtime": 1747221640258, "results": "301", "hashOfConfig": "262"}, {"size": 7752, "mtime": 1747237735157, "results": "302", "hashOfConfig": "252"}, {"size": 9167, "mtime": 1752381444381, "results": "303", "hashOfConfig": "252"}, {"size": 9820, "mtime": 1747279005156, "results": "304", "hashOfConfig": "252"}, {"size": 7977, "mtime": 1752360488635, "results": "305", "hashOfConfig": "252"}, {"size": 6763, "mtime": 1752381456993, "results": "306", "hashOfConfig": "252"}, {"size": 9620, "mtime": 1747242054549, "results": "307", "hashOfConfig": "252"}, {"size": 9567, "mtime": 1747242099457, "results": "308", "hashOfConfig": "252"}, {"size": 5262, "mtime": 1747242002891, "results": "309", "hashOfConfig": "252"}, {"size": 7534, "mtime": 1752351193762, "results": "310", "hashOfConfig": "252"}, {"size": 28904, "mtime": 1752248907873, "results": "311", "hashOfConfig": "252"}, {"size": 1536, "mtime": 1747237627552, "results": "312", "hashOfConfig": "252"}, {"size": 2783, "mtime": 1747275467037, "results": "313", "hashOfConfig": "252"}, {"size": 4730, "mtime": 1747275582856, "results": "314", "hashOfConfig": "252"}, {"size": 5097, "mtime": 1747302359006, "results": "315", "hashOfConfig": "262"}, {"size": 1008, "mtime": 1752283918060, "results": "316", "hashOfConfig": "252"}, {"size": 9653, "mtime": 1752283637791, "results": "317", "hashOfConfig": "252"}, {"size": 358, "mtime": 1752254238558, "results": "318", "hashOfConfig": "252"}, {"size": 3337, "mtime": 1752254433306, "results": "319", "hashOfConfig": "252"}, {"size": 1433, "mtime": 1752283753270, "results": "320", "hashOfConfig": "252"}, {"size": 3435, "mtime": 1752283114606, "results": "321", "hashOfConfig": "252"}, {"size": 4212, "mtime": 1752287116909, "results": "322", "hashOfConfig": "252"}, {"size": 1083, "mtime": 1752283244823, "results": "323", "hashOfConfig": "252"}, {"size": 5685, "mtime": 1752287194809, "results": "324", "hashOfConfig": "252"}, {"size": 16723, "mtime": 1752348343896, "results": "325", "hashOfConfig": "252"}, {"size": 14378, "mtime": 1752304560424, "results": "326", "hashOfConfig": "252"}, {"size": 16323, "mtime": 1747156326467, "results": "327", "hashOfConfig": "252"}, {"size": 8275, "mtime": 1752346642724, "results": "328", "hashOfConfig": "252"}, {"size": 11030, "mtime": 1752346737219, "results": "329", "hashOfConfig": "252"}, {"size": 15352, "mtime": 1752346794610, "results": "330", "hashOfConfig": "252"}, {"size": 4097, "mtime": 1752383420244, "results": "331", "hashOfConfig": "252"}, {"size": 10639, "mtime": 1752347902146, "results": "332", "hashOfConfig": "252"}, {"size": 14557, "mtime": 1752347930663, "results": "333", "hashOfConfig": "252"}, {"size": 12835, "mtime": 1752351140445, "results": "334", "hashOfConfig": "252"}, {"size": 9844, "mtime": 1752354895907, "results": "335", "hashOfConfig": "252"}, {"size": 5972, "mtime": 1752381380447, "results": "336", "hashOfConfig": "252"}, {"size": 7170, "mtime": 1752353841725, "results": "337", "hashOfConfig": "252"}, {"size": 6868, "mtime": 1752381414561, "results": "338", "hashOfConfig": "252"}, {"size": 4273, "mtime": 1752383583814, "results": "339", "hashOfConfig": "252"}, {"size": 18226, "mtime": 1752381116505, "results": "340", "hashOfConfig": "252"}, {"size": 11814, "mtime": 1752387655406, "results": "341", "hashOfConfig": "252"}, {"size": 13854, "mtime": 1752388250382, "results": "342", "hashOfConfig": "252"}, {"size": 16044, "mtime": 1752388174617, "results": "343", "hashOfConfig": "252"}, {"size": 6606, "mtime": 1752387791643, "results": "344", "hashOfConfig": "252"}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "poe9py", {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "14ofb3m", {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qkekr7", {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15319ot", {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx", ["855"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx", ["856", "857", "858"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx", ["859"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx", ["860"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx", ["861", "862"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx", ["863", "864", "865"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx", ["866"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx", ["867", "868"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx", ["869", "870", "871"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx", [], ["872"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx", ["873"], ["874"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx", [], ["875"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx", [], ["876"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx", [], ["877"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx", ["878"], ["879"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx", ["880"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx", ["881", "882"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx", ["883", "884", "885"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx", ["886", "887"], ["888"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx", ["889", "890"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx", ["891", "892", "893", "894"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx", [], ["895"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx", [], ["896"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx", ["897"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx", ["898"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx", ["899"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx", ["900"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx", ["901", "902"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx", ["903", "904"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts", ["905"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts", ["906"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx", ["907"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx", ["908", "909", "910"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/authService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminLogin.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/ScholarshipManager.tsx", ["911"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/MessagesManager.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/ScholarshipForm.tsx", ["912"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Countries.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Guides.tsx", ["913"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Opportunities.tsx", ["914"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx", ["915", "916"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/GuideManager.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/OpportunityManager.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/imageUtils.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/EnhancedHeader.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Dropdown.tsx", ["917", "918"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/MobileNavigationDropdown.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/ScholarshipsByLevel.tsx", ["919", "920", "921", "922"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/OpportunitiesByType.tsx", ["923", "924"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/sidebarService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalPageLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalSidebar.tsx", ["925", "926"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dataPrefetcher.ts", [], [], {"ruleId": "927", "severity": 1, "message": "928", "line": 170, "column": 7, "nodeType": "929", "messageId": "930", "endLine": 170, "endColumn": 15}, {"ruleId": "931", "severity": 1, "message": "932", "line": 51, "column": 15, "nodeType": "933", "endLine": 51, "endColumn": 74}, {"ruleId": "931", "severity": 1, "message": "932", "line": 57, "column": 15, "nodeType": "933", "endLine": 57, "endColumn": 74}, {"ruleId": "931", "severity": 1, "message": "932", "line": 63, "column": 15, "nodeType": "933", "endLine": 63, "endColumn": 74}, {"ruleId": "927", "severity": 1, "message": "928", "line": 170, "column": 7, "nodeType": "929", "messageId": "930", "endLine": 170, "endColumn": 15}, {"ruleId": "934", "severity": 1, "message": "935", "line": 44, "column": 6, "nodeType": "936", "endLine": 44, "endColumn": 68, "suggestions": "937"}, {"ruleId": "927", "severity": 1, "message": "938", "line": 3, "column": 8, "nodeType": "929", "messageId": "930", "endLine": 3, "endColumn": 19}, {"ruleId": "927", "severity": 1, "message": "939", "line": 31, "column": 11, "nodeType": "929", "messageId": "930", "endLine": 31, "endColumn": 23}, {"ruleId": "931", "severity": 1, "message": "932", "line": 51, "column": 15, "nodeType": "933", "endLine": 51, "endColumn": 74}, {"ruleId": "931", "severity": 1, "message": "932", "line": 57, "column": 15, "nodeType": "933", "endLine": 57, "endColumn": 74}, {"ruleId": "931", "severity": 1, "message": "932", "line": 63, "column": 15, "nodeType": "933", "endLine": 63, "endColumn": 74}, {"ruleId": "927", "severity": 1, "message": "939", "line": 34, "column": 11, "nodeType": "929", "messageId": "930", "endLine": 34, "endColumn": 23}, {"ruleId": "927", "severity": 1, "message": "940", "line": 10, "column": 3, "nodeType": "929", "messageId": "930", "endLine": 10, "endColumn": 8}, {"ruleId": "927", "severity": 1, "message": "941", "line": 15, "column": 24, "nodeType": "929", "messageId": "930", "endLine": 15, "endColumn": 43}, {"ruleId": "927", "severity": 1, "message": "942", "line": 3, "column": 10, "nodeType": "929", "messageId": "930", "endLine": 3, "endColumn": 21}, {"ruleId": "927", "severity": 1, "message": "943", "line": 44, "column": 9, "nodeType": "929", "messageId": "930", "endLine": 44, "endColumn": 17}, {"ruleId": "944", "severity": 1, "message": "945", "line": 162, "column": 36, "nodeType": "946", "messageId": "947", "endLine": 162, "endColumn": 60}, {"ruleId": "934", "severity": 1, "message": "948", "line": 37, "column": 6, "nodeType": "936", "endLine": 37, "endColumn": 42, "suggestions": "949", "suppressions": "950"}, {"ruleId": "927", "severity": 1, "message": "951", "line": 236, "column": 9, "nodeType": "929", "messageId": "930", "endLine": 236, "endColumn": 30}, {"ruleId": "934", "severity": 1, "message": "948", "line": 64, "column": 6, "nodeType": "936", "endLine": 64, "endColumn": 61, "suggestions": "952", "suppressions": "953"}, {"ruleId": "934", "severity": 1, "message": "948", "line": 29, "column": 6, "nodeType": "936", "endLine": 29, "endColumn": 31, "suggestions": "954", "suppressions": "955"}, {"ruleId": "934", "severity": 1, "message": "956", "line": 25, "column": 6, "nodeType": "936", "endLine": 25, "endColumn": 8, "suggestions": "957", "suppressions": "958"}, {"ruleId": "934", "severity": 1, "message": "959", "line": 177, "column": 6, "nodeType": "936", "endLine": 177, "endColumn": 8, "suggestions": "960", "suppressions": "961"}, {"ruleId": "927", "severity": 1, "message": "942", "line": 3, "column": 10, "nodeType": "929", "messageId": "930", "endLine": 3, "endColumn": 21}, {"ruleId": "934", "severity": 1, "message": "962", "line": 37, "column": 6, "nodeType": "936", "endLine": 37, "endColumn": 8, "suggestions": "963", "suppressions": "964"}, {"ruleId": "934", "severity": 1, "message": "935", "line": 44, "column": 6, "nodeType": "936", "endLine": 44, "endColumn": 68, "suggestions": "965"}, {"ruleId": "931", "severity": 1, "message": "932", "line": 135, "column": 21, "nodeType": "933", "endLine": 135, "endColumn": 96}, {"ruleId": "931", "severity": 1, "message": "932", "line": 141, "column": 21, "nodeType": "933", "endLine": 141, "endColumn": 96}, {"ruleId": "931", "severity": 1, "message": "932", "line": 108, "column": 21, "nodeType": "933", "endLine": 108, "endColumn": 94}, {"ruleId": "931", "severity": 1, "message": "932", "line": 114, "column": 21, "nodeType": "933", "endLine": 114, "endColumn": 94}, {"ruleId": "931", "severity": 1, "message": "932", "line": 120, "column": 21, "nodeType": "933", "endLine": 120, "endColumn": 94}, {"ruleId": "927", "severity": 1, "message": "966", "line": 2, "column": 105, "nodeType": "929", "messageId": "930", "endLine": 2, "endColumn": 111}, {"ruleId": "927", "severity": 1, "message": "967", "line": 3, "column": 111, "nodeType": "929", "messageId": "930", "endLine": 3, "endColumn": 126}, {"ruleId": "934", "severity": 1, "message": "948", "line": 45, "column": 6, "nodeType": "936", "endLine": 45, "endColumn": 31, "suggestions": "968", "suppressions": "969"}, {"ruleId": "927", "severity": 1, "message": "943", "line": 44, "column": 9, "nodeType": "929", "messageId": "930", "endLine": 44, "endColumn": 17}, {"ruleId": "934", "severity": 1, "message": "970", "line": 106, "column": 6, "nodeType": "936", "endLine": 106, "endColumn": 8, "suggestions": "971"}, {"ruleId": "931", "severity": 1, "message": "932", "line": 117, "column": 17, "nodeType": "933", "endLine": 117, "endColumn": 131}, {"ruleId": "931", "severity": 1, "message": "932", "line": 125, "column": 17, "nodeType": "933", "endLine": 125, "endColumn": 131}, {"ruleId": "931", "severity": 1, "message": "932", "line": 133, "column": 17, "nodeType": "933", "endLine": 133, "endColumn": 131}, {"ruleId": "931", "severity": 1, "message": "932", "line": 141, "column": 17, "nodeType": "933", "endLine": 141, "endColumn": 131}, {"ruleId": "934", "severity": 1, "message": "962", "line": 37, "column": 6, "nodeType": "936", "endLine": 37, "endColumn": 8, "suggestions": "972", "suppressions": "973"}, {"ruleId": "934", "severity": 1, "message": "956", "line": 25, "column": 6, "nodeType": "936", "endLine": 25, "endColumn": 8, "suggestions": "974", "suppressions": "975"}, {"ruleId": "927", "severity": 1, "message": "976", "line": 38, "column": 9, "nodeType": "929", "messageId": "930", "endLine": 38, "endColumn": 12}, {"ruleId": "931", "severity": 1, "message": "932", "line": 101, "column": 26, "nodeType": "933", "endLine": 101, "endColumn": 87}, {"ruleId": "927", "severity": 1, "message": "977", "line": 2, "column": 40, "nodeType": "929", "messageId": "930", "endLine": 2, "endColumn": 44}, {"ruleId": "934", "severity": 1, "message": "978", "line": 38, "column": 6, "nodeType": "936", "endLine": 38, "endColumn": 8, "suggestions": "979"}, {"ruleId": "931", "severity": 1, "message": "932", "line": 186, "column": 21, "nodeType": "933", "endLine": 186, "endColumn": 74}, {"ruleId": "931", "severity": 1, "message": "932", "line": 188, "column": 21, "nodeType": "933", "endLine": 188, "endColumn": 74}, {"ruleId": "927", "severity": 1, "message": "977", "line": 4, "column": 30, "nodeType": "929", "messageId": "930", "endLine": 4, "endColumn": 34}, {"ruleId": "927", "severity": 1, "message": "980", "line": 7, "column": 10, "nodeType": "929", "messageId": "930", "endLine": 7, "endColumn": 16}, {"ruleId": "981", "severity": 1, "message": "982", "line": 172, "column": 1, "nodeType": "983", "endLine": 179, "endColumn": 3}, {"ruleId": "927", "severity": 1, "message": "980", "line": 2, "column": 10, "nodeType": "929", "messageId": "930", "endLine": 2, "endColumn": 16}, {"ruleId": "927", "severity": 1, "message": "984", "line": 5, "column": 16, "nodeType": "929", "messageId": "930", "endLine": 5, "endColumn": 20}, {"ruleId": "927", "severity": 1, "message": "985", "line": 13, "column": 10, "nodeType": "929", "messageId": "930", "endLine": 13, "endColumn": 20}, {"ruleId": "927", "severity": 1, "message": "986", "line": 15, "column": 10, "nodeType": "929", "messageId": "930", "endLine": 15, "endColumn": 21}, {"ruleId": "934", "severity": 1, "message": "987", "line": 106, "column": 6, "nodeType": "936", "endLine": 106, "endColumn": 8, "suggestions": "988"}, {"ruleId": "927", "severity": 1, "message": "989", "line": 10, "column": 3, "nodeType": "929", "messageId": "930", "endLine": 10, "endColumn": 12}, {"ruleId": "927", "severity": 1, "message": "939", "line": 32, "column": 11, "nodeType": "929", "messageId": "930", "endLine": 32, "endColumn": 23}, {"ruleId": "934", "severity": 1, "message": "990", "line": 26, "column": 6, "nodeType": "936", "endLine": 26, "endColumn": 24, "suggestions": "991"}, {"ruleId": "934", "severity": 1, "message": "992", "line": 35, "column": 6, "nodeType": "936", "endLine": 35, "endColumn": 15, "suggestions": "993"}, {"ruleId": "927", "severity": 1, "message": "939", "line": 31, "column": 11, "nodeType": "929", "messageId": "930", "endLine": 31, "endColumn": 23}, {"ruleId": "934", "severity": 1, "message": "994", "line": 45, "column": 6, "nodeType": "936", "endLine": 45, "endColumn": 35, "suggestions": "995"}, {"ruleId": "934", "severity": 1, "message": "996", "line": 86, "column": 6, "nodeType": "936", "endLine": 86, "endColumn": 14, "suggestions": "997"}, {"ruleId": "934", "severity": 1, "message": "996", "line": 98, "column": 6, "nodeType": "936", "endLine": 98, "endColumn": 14, "suggestions": "998"}, {"ruleId": "927", "severity": 1, "message": "999", "line": 4, "column": 8, "nodeType": "929", "messageId": "930", "endLine": 4, "endColumn": 31}, {"ruleId": "927", "severity": 1, "message": "939", "line": 30, "column": 11, "nodeType": "929", "messageId": "930", "endLine": 30, "endColumn": 23}, {"ruleId": "934", "severity": 1, "message": "1000", "line": 44, "column": 6, "nodeType": "936", "endLine": 44, "endColumn": 33, "suggestions": "1001"}, {"ruleId": "1002", "severity": 2, "message": "1003", "line": 89, "column": 6, "nodeType": "1004", "messageId": "1005", "endLine": 89, "endColumn": 21}, {"ruleId": "927", "severity": 1, "message": "939", "line": 31, "column": 11, "nodeType": "929", "messageId": "930", "endLine": 31, "endColumn": 23}, {"ruleId": "934", "severity": 1, "message": "1006", "line": 49, "column": 6, "nodeType": "936", "endLine": 49, "endColumn": 32, "suggestions": "1007"}, {"ruleId": "927", "severity": 1, "message": "939", "line": 33, "column": 11, "nodeType": "929", "messageId": "930", "endLine": 33, "endColumn": 23}, {"ruleId": "934", "severity": 1, "message": "1008", "line": 43, "column": 6, "nodeType": "936", "endLine": 43, "endColumn": 14, "suggestions": "1009"}, "@typescript-eslint/no-unused-vars", "'features' is assigned a value but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchScholarships'. Either include it or remove the dependency array.", "ArrayExpression", ["1010"], "'AdminLayout' is defined but never used.", "'translations' is assigned a value but never used.", "'Space' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'useAdminApi' is defined but never used.", "'navigate' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'id'.", "ArrowFunctionExpression", "unsafeRefs", "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["1011"], ["1012"], "'sendEmailNotification' is assigned a value but never used.", ["1013"], ["1014"], ["1015"], ["1016"], "React Hook useEffect has a missing dependency: 'initializeSetup'. Either include it or remove the dependency array.", ["1017"], ["1018"], "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["1019"], ["1020"], "React Hook useEffect has a missing dependency: 'fetchSettings'. Either include it or remove the dependency array.", ["1021"], ["1022"], ["1023"], "'Select' is defined but never used.", "'FilePdfOutlined' is defined but never used.", ["1024"], ["1025"], "React Hook useEffect has missing dependencies: 'adminInfo' and 'stats'. Either include them or remove the dependency array.", ["1026"], ["1027"], ["1028"], ["1029"], ["1030"], "'api' is assigned a value but never used.", "'Spin' is defined but never used.", "React Hook useEffect has a missing dependency: 'backgrounds.length'. Either include it or remove the dependency array.", ["1031"], "'getEnv' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'Text' is assigned a value but never used.", "'DateFormat' is defined but never used.", "'ApiResponse' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSecurityEvents'. Either include it or remove the dependency array.", ["1032"], "'XMarkIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchGuides'. Either include it or remove the dependency array.", ["1033"], "React Hook useEffect has a missing dependency: 'fetchOpportunities'. Either include it or remove the dependency array.", ["1034"], "React Hook useEffect has missing dependencies: 'fetchScholarships' and 'fetchStatistics'. Either include them or remove the dependency array.", ["1035"], "React Hook useEffect has a missing dependency: 'closeDropdown'. Either include it or remove the dependency array.", ["1036"], ["1037"], "'EnhancedScholarshipCard' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchLevelStatistics' and 'fetchScholarshipsByLevel'. Either include them or remove the dependency array.", ["1038"], "react/jsx-no-undef", "'ScholarshipCard' is not defined.", "JSXIdentifier", "undefined", "React Hook useEffect has missing dependencies: 'fetchOpportunitiesByType' and 'fetchTypeStatistics'. Either include them or remove the dependency array.", ["1039"], "React Hook useEffect has a missing dependency: 'fetchSidebarData'. Either include it or remove the dependency array.", ["1040"], {"desc": "1041", "fix": "1042"}, {"desc": "1043", "fix": "1044"}, {"kind": "1045", "justification": "1046"}, {"desc": "1047", "fix": "1048"}, {"kind": "1045", "justification": "1046"}, {"desc": "1049", "fix": "1050"}, {"kind": "1045", "justification": "1046"}, {"desc": "1051", "fix": "1052"}, {"kind": "1045", "justification": "1046"}, {"desc": "1053", "fix": "1054"}, {"kind": "1045", "justification": "1046"}, {"desc": "1055", "fix": "1056"}, {"kind": "1045", "justification": "1046"}, {"desc": "1041", "fix": "1057"}, {"desc": "1049", "fix": "1058"}, {"kind": "1045", "justification": "1046"}, {"desc": "1059", "fix": "1060"}, {"desc": "1055", "fix": "1061"}, {"kind": "1045", "justification": "1046"}, {"desc": "1051", "fix": "1062"}, {"kind": "1045", "justification": "1046"}, {"desc": "1063", "fix": "1064"}, {"desc": "1065", "fix": "1066"}, {"desc": "1067", "fix": "1068"}, {"desc": "1069", "fix": "1070"}, {"desc": "1071", "fix": "1072"}, {"desc": "1073", "fix": "1074"}, {"desc": "1073", "fix": "1075"}, {"desc": "1076", "fix": "1077"}, {"desc": "1078", "fix": "1079"}, {"desc": "1080", "fix": "1081"}, "Update the dependencies array to be: [pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", {"range": "1082", "text": "1083"}, "Update the dependencies array to be: [applyFilters, messages, searchTerm, statusFilter]", {"range": "1084", "text": "1085"}, "directive", "", "Update the dependencies array to be: [scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", {"range": "1086", "text": "1087"}, "Update the dependencies array to be: [subscribers, searchTerm, applyFilters]", {"range": "1088", "text": "1089"}, "Update the dependencies array to be: [initializeSetup]", {"range": "1090", "text": "1091"}, "Update the dependencies array to be: [fetchAnalyticsData]", {"range": "1092", "text": "1093"}, "Update the dependencies array to be: [fetchSettings]", {"range": "1094", "text": "1095"}, {"range": "1096", "text": "1083"}, {"range": "1097", "text": "1089"}, "Update the dependencies array to be: [adminInfo, stats]", {"range": "1098", "text": "1099"}, {"range": "1100", "text": "1095"}, {"range": "1101", "text": "1091"}, "Update the dependencies array to be: [backgrounds.length]", {"range": "1102", "text": "1103"}, "Update the dependencies array to be: [fetchSecurityEvents]", {"range": "1104", "text": "1105"}, "Update the dependencies array to be: [fetchGuides, selectedCategory]", {"range": "1106", "text": "1107"}, "Update the dependencies array to be: [fetchOpportunities, filters]", {"range": "1108", "text": "1109"}, "Update the dependencies array to be: [decodedCountry, currentPage, fetchScholarships, fetchStatistics]", {"range": "1110", "text": "1111"}, "Update the dependencies array to be: [closeDropdown, isOpen]", {"range": "1112", "text": "1113"}, {"range": "1114", "text": "1113"}, "Update the dependencies array to be: [decodedLevel, currentPage, fetchScholarshipsByLevel, fetchLevelStatistics]", {"range": "1115", "text": "1116"}, "Update the dependencies array to be: [decodedType, currentPage, fetchOpportunitiesByType, fetchTypeStatistics]", {"range": "1117", "text": "1118"}, "Update the dependencies array to be: [config, fetchSidebarData]", {"range": "1119", "text": "1120"}, [1225, 1287], "[pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", [1158, 1194], "[applyFilters, messages, searchTerm, statusFilter]", [1966, 2021], "[scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", [903, 928], "[subscribers, searchTerm, applyFilters]", [969, 971], "[initializeSetup]", [6083, 6085], "[fetchAnalyticsData]", [1255, 1257], "[fetchSettings]", [1228, 1290], [1914, 1939], [3435, 3437], "[adminInfo, stats]", [1201, 1203], [969, 971], [1326, 1328], "[backgrounds.length]", [3118, 3120], "[fetchSecurityEvents]", [734, 752], "[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>]", [934, 943], "[fetchOpportunities, filters]", [1370, 1399], "[decodedCountry, currentPage, fetchScholarships, fetchStatistics]", [2324, 2332], "[closeDropdown, isOpen]", [2652, 2660], [1402, 1429], "[decodedLevel, currentPage, fetchScholarshipsByLevel, fetchLevelStatistics]", [1499, 1525], "[decodedType, currentPage, fetchOpportunitiesByType, fetchTypeStatistics]", [1163, 1171], "[config, fetchSidebarData]"]